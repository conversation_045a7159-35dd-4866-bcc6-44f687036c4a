/**
 * 光伏消纳曲线图表组件
 */
import {
  processDashedSeries,
  createChartOption,
} from "../config/ChartConfig.js";

class SolarChart {
  constructor(containerId, data) {
    this.container = document.getElementById(containerId);
    this.data = data;
    this.chart = null;
    this.init();
  }

  init() {
    if (!this.container) {
      console.error("容器元素不存在");
      return;
    }

    // 初始化ECharts实例
    this.chart = echarts.init(this.container);
    this.render();

    // 添加窗口大小变化的监听器
    window.addEventListener("resize", () => {
      this.chart.resize();
    });
  }

  render() {
    const { xAxis, series, unit, yAxis } = this.data;

    // 使用公共配置处理实线虚线分割的系列数据
    const seriesData = processDashedSeries(series);

    // 使用公共配置创建图表选项
    const option = createChartOption({
      xAxis,
      series: seriesData,
      unit,
      yAxis,
      gridConfig: {
        left: 5,
        top: "25%",
      },
      yAxisConfig: {},
    });

    this.chart.setOption(option);
  }

  // 更新数据
  updateData(newData) {
    this.data = newData;
    this.render();
  }

  // 销毁图表
  destroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener("resize", this.chart.resize);
  }
}

export default SolarChart;
