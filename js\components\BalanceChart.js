/**
 * 平衡曲线图表组件
 */
import {
  processDashedSeries,
  createChartOption,
  ColorOptions,
  addMarkersToSeries,
} from "../config/ChartConfig.js";

class BalanceChart {
  constructor(containerId, data) {
    this.container = document.getElementById(containerId);
    this.data = data;
    this.chart = null;
    this.init();
  }

  init() {
    if (!this.container) {
      console.error("容器元素不存在");
      return;
    }

    // 延迟初始化，确保DOM布局完成
    setTimeout(() => {
      // 设置容器自适应高度
      this.setupContainerHeight();

      // 初始化ECharts实例
      this.chart = echarts.init(this.container);
      this.render();
    }, 100);

    // 添加窗口大小变化的监听器
    window.addEventListener("resize", () => {
      this.setupContainerHeight();
    });
  }

  /**
   * 设置容器自适应高度
   */
  setupContainerHeight() {
    // 获取父容器的可用高度
    const parentContainer = this.container.closest(".chart-container");
    if (!parentContainer) return;

    // 使用 requestAnimationFrame 确保在布局完成后计算高度
    requestAnimationFrame(() => {
      // 计算标题高度和内边距
      const titleElement = parentContainer.querySelector(".chart-title");
      const titleHeight = titleElement ? titleElement.offsetHeight : 0;
      const containerPadding = 30; // 15px * 2 (上下内边距)
      const titleMargin = 10; // 标题下边距

      // 获取父容器的实际高度
      const parentHeight = parentContainer.offsetHeight;

      // 如果父容器高度为0，说明布局还未完成，使用默认高度
      if (parentHeight === 0) {
        this.container.style.height = "300px";
        return;
      }

      // 计算图表可用高度
      const availableHeight =
        parentHeight - titleHeight - containerPadding - titleMargin;

      // 设置最小高度，确保图表有足够的显示空间
      const minHeight = 250;
      const chartHeight = Math.max(availableHeight, minHeight);

      // 应用高度
      this.container.style.height = `${chartHeight}px`;

      // 如果图表已经初始化，触发重新渲染
      if (this.chart) {
        this.chart.resize();
      }
    });
  }

  render() {
    const { xAxis, series, unit } = this.data;
    console.log("🚀 ~ BalanceChart ~ render ~ series:", series);

    // 使用公共配置处理实线虚线分割的系列数据，包括标记线
    const seriesData = processDashedSeries(series, 0.6, this.data.markers);
    // console.log("🚀 ~ BalanceChart ~ render ~ seriesData:", seriesData);

    seriesData.forEach((item) => {
      const isFirst = item.name === "全网可用最大";
      const markPointConfig = {
        symbolSize: 40,
        symbol: "pin",
        symbolRotate: isFirst ? 0 : 180,
        label: {
          color: ColorOptions[item.colorType].color,
          position: isFirst ? "top" : "bottom",
          formatter: "{c}",
        },
        itemStyle: {
          borderWidth: 4,
        },
      };

      item.markPoint = {
        ...item.markPoint,
        ...markPointConfig,
      };
    });
    console.log(
      "🚀 ~ BalanceChart ~ seriesData.forEach ~ seriesData:",
      seriesData
    );

    // addMarkersToSeries(seriesData, this.data.markers);

    // {
    //   symbolSize: 60,
    //   symbol: "pin",
    //   itemStyle: {
    //     borderWidth: 4,
    //   },
    //   label: {
    //     show: true,
    //     position: "top",
    //   },
    // }

    // 使用公共配置创建图表选项
    const option = createChartOption({
      xAxis,
      series: seriesData,
      unit,
      gridConfig: {
        top: "15%",
      },
    });

    this.chart.setOption(option);
  }

  // 更新数据
  updateData(newData) {
    this.data = newData;
    this.render();
  }

  // 销毁图表
  destroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener("resize", this.chart.resize);
  }
}

export default BalanceChart;
