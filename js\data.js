/**
 * 电网监控系统模拟数据
 */

// 时间轴数据（小时）
const timeAxis = [
  "00",
  "02",
  "04",
  "06",
  "08",
  "10",
  "12",
  "14",
  "16",
  "18",
  "20",
  "22",
];

// 平衡曲线数据
const balanceData = {
  title: "平衡曲线",
  unit: "单位:万千瓦",
  xAxis: [
    "08-09 00",
    "02",
    "04",
    "06",
    "08",
    "10",
    "12",
    "14",
    "16",
    "18",
    "20",
    "22",
    "08-10 00",
  ],
  series: [
    {
      name: "全网可用最大",
      colorType: "green",
      data: [
        11500, 11000, 10800, 11200, 11800, 12500, 13000, 13581, 13200, 12800,
        12206, 11800, 11500,
      ],
      currentValue: { value: 13581, position: 7 },
    },
    {
      name: "全网可用最小",
      colorType: "blue",
      data: [
        7500, 7000, 6800, 7200, 7800, 8500, 9200, 10000, 10579, 9800, 9000,
        8500, 8000,
      ],
      currentValue: { value: 10579, position: 8 },
    },
    {
      name: "全网用电",
      colorType: "yellow",
      data: [
        10000, 9500, 9150, 9800, 10500, 11200, 11800, 12003, 11500, 11000,
        10500, 10200, 10000,
      ],
      currentValue: { value: 12003, position: 7 },
    },
  ],
  markers: [
    {
      value: 1253,
      text: "负备用最小值:1253",
      position: { x: 6, y: 6000 },
      colorType: "blue",
    },
    {
      value: 781,
      text: "正备用最小值:781",
      position: { x: 7, y: 12800 },
      colorType: "green",
    },
  ],
};

// 风电消纳曲线数据
const windData = {
  title: "风电消纳曲线",
  unit: "单位:万千瓦",
  xAxis: timeAxis,
  series: [
    {
      name: "风电",
      colorType: "green",
      data: [600, 650, 680, 700, 690, 680, 672.9, 650, 630, 610, 600, 590],
      currentValue: { value: 672.9, position: 6 },
    },
    {
      name: "风电消纳(含深调能力)",
      colorType: "blue",
      data: [
        1200, 1300, 1350, 1400, 1380, 1422.8, 1400, 1380, 1350, 1320, 1300,
        1280,
      ],
      currentValue: { value: 1422.8, position: 5 },
    },
  ],
  yAxis: {
    min: 200,
    max: 2500,
  },
};

// 光伏消纳曲线数据
const solarData = {
  title: "光伏消纳曲线",
  unit: "单位:万千瓦",
  xAxis: timeAxis,
  series: [
    {
      name: "统调光伏",
      colorType: "green",
      data: [0, 0, 0, 50, 120, 180, 211.4, 200, 150, 80, 20, 0],
      currentValue: { value: 211.4, position: 6 },
    },
    {
      name: "统调光伏消纳",
      colorType: "blue",
      data: [0, 0, 0, 100, 250, 380, 495.2, 450, 350, 200, 50, 0],
      currentValue: { value: 495.2, position: 6 },
    },
  ],
  yAxis: {
    min: 0,
    max: 600,
  },
};

// 断面监视表格数据
const sectionData = {
  title: "断面监视",
  columns: ["断面名称", "限额", "预测潮流", "差额", "越限时间"],
  data: [
    ["XXX 断面名称1", "XXX", "XXX 预测潮流1", "XXX", "2025-06-13 04:00"],
    ["XXX 断面名称2", "XXX", "XXX 预测潮流2", "XXX", "2025-06-13 04:00"],
    ["XXX 断面名称3", "XXX", "XXX 预测潮流3", "XXX", "2025-06-13 04:00"],
    ["XXX 断面名称4", "XXX", "XXX 预测潮流4", "XXX", "2025-06-13 04:00"],
    ["XXX 断面名称5", "XXX", "XXX 预测潮流5", "XXX", "2025-06-13 04:00"],
  ],
};

// CPS曲线数据
const cpsData = {
  xAxis: ["01", "02", "03", "04", "05", "06", "07"],
  series: [
    {
      name: "当班CPS1",
      colorType: "green",
      showSymbol: true,
      data: [160, 100, 140, 210, 180, 120, 150],
    },
    {
      name: "当班CPS2",
      colorType: "blue",
      showSymbol: true,
      data: [90, 110, 130, 170, 140, 100, 150],
    },
  ],
  yAxis: {
    min: 0,
    max: 300,
  },
};

// 当班费用和实时ACE数据
const infoData = {
  currentCost: "XXXX",
  realTimeACE: "XXXX",
};

export { balanceData, windData, solarData, sectionData, cpsData, infoData };
